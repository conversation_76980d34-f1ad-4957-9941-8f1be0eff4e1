# 变更日志

本文档记录了 AI Reading Extension 项目的所有重要变更。

## v1.8.0 (2024-05-15)

### 新增功能

- 采用全新的内容处理流程，提供更一致、干净的阅读体验
- 实现内容处理管道，包括内容提取、Markdown 转换和渲染三个阶段
- 添加 defuddle 内容提取器，智能识别文章主体
- 添加 turndown 转换器，将 HTML 转换为标准 Markdown
- 添加 markdown-it 渲染器，将 Markdown 渲染为美观的 HTML
- 添加阅读工具栏，包含常用阅读工具和功能
- 添加文本选择工具，支持复制、高亮、注释等功能
- 添加阅读进度显示，记住上次阅读位置

### 改进

- 重新设计阅读设置面板，提供更多自定义选项
- 优化代码块显示，支持更多语言和更好的高亮效果
- 优化图片显示，支持懒加载和更好的缩放效果
- 优化表格显示，支持更好的排版和滚动效果
- 优化列表显示，支持更好的缩进和样式
- 优化性能，提高页面加载和渲染速度
- 优化内存使用，减少内存占用
- 优化错误处理，提供更友好的错误提示

### 修复

- 修复了在某些网站上内容提取失败的问题
- 修复了代码块高亮不正确的问题
- 修复了图片加载失败的问题
- 修复了表格结构错乱的问题
- 修复了列表缩进不正确的问题
- 修复了字体大小设置不生效的问题
- 修复了主题切换不完全的问题
- 修复了多次进入阅读模式导致的样式问题
- 修复了启用阅读模式时出现 "TypeError: _e.record is not a function" 错误的问题
- 修复了启用阅读模式时出现 "TypeError: undefined is not a string, or an element/document/fragment node" 错误的问题
- 修复了处理特殊 URL（如 about:blank）时出现的 "Failed to construct 'URL': Invalid URL" 错误
- 修复了 DefuddleExtractor 类中的方法重载问题
- 统一了所有默认设置，解决了设置值不一致的问题

## v1.5.1 (2024-03-20)

### 新增功能

- 添加了代码块主题选择功能
- 添加了代码块行号显示功能
- 添加了代码块复制按钮
- 添加了代码块语言显示

### 改进

- 优化了代码块的样式，采用 GitHub 风格
- 优化了代码块的工具栏，更加直观易用
- 优化了代码块的高亮效果，支持更多语言
- 优化了代码块的响应式布局

### 修复

- 修复了代码块在暗色模式下的显示问题
- 修复了代码块复制功能在某些情况下失效的问题
- 修复了代码块语言识别不准确的问题
- 修复了代码块行号与代码不对齐的问题

## v1.5.0 (2024-02-28)

### 新增功能

- 添加了图片查看器，支持缩放和全屏查看
- 添加了表格增强功能，支持排序和过滤
- 添加了列表增强功能，支持折叠和展开
- 添加了文本选择工具栏，支持复制和搜索

### 改进

- 优化了阅读模式的性能，减少内存占用
- 优化了阅读模式的样式，提供更好的阅读体验
- 优化了阅读模式的响应式布局，适配不同屏幕尺寸
- 优化了阅读模式的加载速度，减少等待时间

### 修复

- 修复了在某些网站上无法正确提取内容的问题
- 修复了图片加载失败的问题
- 修复了表格结构错乱的问题
- 修复了列表缩进不正确的问题

## v1.4.3 (2024-01-15)

### 改进

- 优化了扩展图标的点击行为，提升用户体验
- 在非阅读模式下，直接点击扩展图标即可进入阅读模式
- 在阅读模式下，点击扩展图标会打开设置面板
- 简化了用户操作流程，减少了进入阅读模式所需的点击次数

## v1.4.2 (2023-12-28)

### 改进

- 重新设计代码块样式，采用极简风格
- 优化代码块工具栏，更加直观易用
- 优化代码高亮效果，支持更多语言
- 限制页面最大宽度为 1800px，提供更好的阅读体验
- 添加设置重置按钮，方便用户恢复默认设置

### 修复

- 修复了字体大小设置不影响所有文本的问题
- 修复了代码块高亮不正确的问题
- 修复了暗色模式下的显示问题
- 修复了多次进入阅读模式导致的样式问题

## v1.4.1 (2023-12-15)

### 改进

- 优化了代码块的样式和交互
- 优化了图片的加载和显示
- 优化了表格的样式和结构
- 优化了列表的样式和缩进

### 修复

- 修复了懒加载图片不显示的问题
- 修复了代码块语言识别不准确的问题
- 修复了表格结构错乱的问题
- 修复了列表缩进不正确的问题

## v1.4.0 (2023-12-01)

### 新增功能

- 添加了阅读模式设置面板
- 添加了字体大小调整功能
- 添加了主题切换功能
- 添加了显示/隐藏图片功能
- 添加了显示/隐藏目录功能

### 改进

- 优化了阅读模式的性能和稳定性
- 优化了内容提取算法，提高准确率
- 优化了页面样式，提供更好的阅读体验
- 优化了响应式布局，适配不同屏幕尺寸

### 修复

- 修复了在某些网站上无法正确提取内容的问题
- 修复了图片加载失败的问题
- 修复了代码块高亮不正确的问题
- 修复了表格结构错乱的问题

## v1.3.0 (2023-11-15)

### 新增功能

- 添加了代码块高亮功能
- 添加了表格增强功能
- 添加了图片增强功能
- 添加了列表增强功能

### 改进

- 优化了阅读模式的性能和稳定性
- 优化了内容提取算法，提高准确率
- 优化了页面样式，提供更好的阅读体验
- 优化了响应式布局，适配不同屏幕尺寸

### 修复

- 修复了在某些网站上无法正确提取内容的问题
- 修复了图片加载失败的问题
- 修复了代码块高亮不正确的问题
- 修复了表格结构错乱的问题

## v1.2.0 (2023-10-30)

### 新增功能

- 添加了目录生成功能
- 添加了退出阅读模式按钮
- 添加了阅读进度显示
- 添加了快捷键支持

### 改进

- 优化了阅读模式的性能和稳定性
- 优化了内容提取算法，提高准确率
- 优化了页面样式，提供更好的阅读体验
- 优化了响应式布局，适配不同屏幕尺寸

### 修复

- 修复了在某些网站上无法正确提取内容的问题
- 修复了图片加载失败的问题
- 修复了代码块高亮不正确的问题
- 修复了表格结构错乱的问题

## v1.1.0 (2023-10-15)

### 新增功能

- 添加了暗色模式支持
- 添加了字体大小调整功能
- 添加了行间距调整功能
- 添加了段落间距调整功能

### 改进

- 优化了阅读模式的性能和稳定性
- 优化了内容提取算法，提高准确率
- 优化了页面样式，提供更好的阅读体验
- 优化了响应式布局，适配不同屏幕尺寸

### 修复

- 修复了在某些网站上无法正确提取内容的问题
- 修复了图片加载失败的问题
- 修复了代码块高亮不正确的问题
- 修复了表格结构错乱的问题

## v1.0.0 (2023-10-01)

### 初始版本

- 基本的阅读模式功能
- 内容提取和优化
- 简单的样式美化
- 基本的响应式布局
