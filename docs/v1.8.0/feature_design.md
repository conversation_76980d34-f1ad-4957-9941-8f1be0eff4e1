# 阅读模式扩展 v1.8.0 功能设计

## 概述

阅读模式扩展 v1.8.0 版本将采用全新的内容处理流程，通过 defuddle 提取内容、turndown 转换为 Markdown、markdown-it 渲染的方式，为用户提供更加一致、干净的阅读体验。本文档详细描述了新版本的功能设计和用户体验改进。

## 核心功能

### 1. 智能内容提取

**功能描述**：使用 defuddle 库智能识别和提取网页的主要内容，过滤广告、导航栏、侧边栏等干扰元素。

**用户价值**：

- 自动识别文章主体，无需手动选择
- 去除广告和干扰元素，提供干净的阅读环境
- 保留重要的图片、表格和代码块

**实现要点**：

- 集成 defuddle 库进行内容提取
- 开发网站特定的提取规则，提高准确性
- 提供手动选择内容区域的备选方案

**用户界面**：

- 提供「智能提取」和「手动选择」两种模式
- 在提取过程中显示加载指示器
- 提供提取质量反馈机制

### 2. Markdown 转换

**功能描述**：使用 turndown 将提取的 HTML 内容转换为标准 Markdown 格式，保留文档结构和格式。

**用户价值**：

- 统一的内容格式，提供一致的阅读体验
- 更好的文本可读性和排版
- 支持导出为 Markdown 文件

**实现要点**：

- 集成 turndown 库进行 HTML 到 Markdown 的转换
- 自定义转换规则，处理特殊元素（代码块、表格、图片等）
- 优化 Markdown 输出，确保可读性

**用户界面**：

- 提供 Markdown 源码查看选项
- 支持一键复制 Markdown 内容
- 提供导出为 .md 文件的功能

### 3. 增强渲染

**功能描述**：使用 markdown-it 将 Markdown 内容渲染为美观的 HTML，支持代码高亮、数学公式、表格等增强功能。

**用户价值**：

- 美观一致的排版和样式
- 代码块语法高亮
- 数学公式和图表支持

**实现要点**：

- 集成 markdown-it 及其插件生态
- 开发自定义主题和样式
- 支持代码高亮、数学公式、表格等特殊元素

**用户界面**：

- 提供多种预设主题（浅色、深色、护眼等）
- 支持自定义字体、颜色和间距
- 提供目录导航功能

## 用户体验改进

### 1. 阅读设置面板

**功能描述**：提供全面的阅读设置面板，允许用户自定义阅读体验。

**设置选项**：

| 设置类别 | 设置项     | 描述                           |
| -------- | ---------- | ------------------------------ |
| **主题** | 主题选择   | 浅色、深色、护眼、自定义       |
|          | 背景颜色   | 自定义背景颜色                 |
|          | 文本颜色   | 自定义文本颜色                 |
| **排版** | 字体       | 多种字体选择                   |
|          | 字号       | 调整文本大小                   |
|          | 行高       | 调整行间距                     |
|          | 段落间距   | 调整段落间距                   |
|          | 页面宽度   | 调整内容宽度                   |
|          | 文本对齐   | 左对齐、居中、右对齐、两端对齐 |
| **内容** | 显示图片   | 开启/关闭图片显示              |
|          | 显示代码块 | 开启/关闭代码块显示            |
|          | 显示目录   | 开启/关闭目录显示              |
|          | 首行缩进   | 开启/关闭首行缩进              |

**用户界面**：

- 浮动设置面板，可随时调出和隐藏
- 分类标签页，便于导航
- 实时预览设置效果
- 保存为预设功能

### 2. 阅读工具栏

**功能描述**：提供固定或浮动的工具栏，包含常用阅读工具和功能。

**工具栏功能**：

- 设置按钮：打开设置面板
- 目录按钮：显示/隐藏目录
- 字体调整：增大/减小字号
- 主题切换：快速切换主题
- 全屏模式：进入/退出全屏
- 分享按钮：分享当前页面
- 导出按钮：导出为 Markdown 或 PDF
- 退出按钮：退出阅读模式

**用户界面**：

- 简洁的图标设计
- 支持固定在顶部或底部
- 可选择浮动模式
- 响应式布局，适应不同屏幕尺寸

### 3. 文本选择工具

**功能描述**：选择文本时显示的工具栏，提供复制、高亮、注释等功能。

**工具功能**：

- 复制：复制选中文本
- 高亮：高亮选中文本（多种颜色）
- 注释：为选中文本添加注释
- 搜索：搜索选中文本
- 分享：分享选中文本

**用户界面**：

- 选择文本时自动显示在附近
- 简洁的图标设计
- 高亮颜色选择器
- 注释编辑弹窗

### 4. 阅读进度

**功能描述**：显示阅读进度，并记住上次阅读位置。

**功能特点**：

- 进度条：显示当前阅读进度
- 位置记忆：记住上次阅读位置
- 阅读时间：估计剩余阅读时间
- 章节导航：显示当前章节位置

**用户界面**：

- 顶部或底部进度条
- 章节导航指示器
- 阅读时间显示
- 返回上次位置按钮

## 性能与可访问性

### 1. 性能优化

**目标**：

- 页面加载时间 < 1 秒
- 内容提取时间 < 2 秒
- 渲染时间 < 500 毫秒
- 内存占用 < 100MB

**优化策略**：

- 懒加载：延迟加载非关键资源
- Web Workers：在后台线程处理内容转换
- 缓存：缓存处理过的页面内容
- 增量渲染：分批处理大型文档

### 2. 可访问性

**功能**：

- 键盘导航：完全支持键盘操作
- 屏幕阅读器：兼容主流屏幕阅读器
- 高对比度模式：提供高对比度主题
- 字体大小：支持大范围的字体大小调整

**快捷键**：

| 功能          | 快捷键 |
| ------------- | ------ |
| 切换阅读模式  | Alt+R  |
| 打开设置      | Alt+S  |
| 切换主题      | Alt+T  |
| 增大字号      | Alt+=  |
| 减小字号      | Alt+-  |
| 显示/隐藏目录 | Alt+C  |
| 全屏模式      | Alt+F  |
| 退出阅读模式  | Esc    |

## 特色功能

### 1. 智能目录生成

**功能描述**：自动从文章内容生成目录，支持多级目录和锚点导航。

**功能特点**：

- 自动识别标题层级
- 支持折叠/展开子目录
- 当前位置高亮
- 点击导航到对应位置

**用户界面**：

- 侧边栏或弹出式目录
- 可调整宽度
- 支持搜索目录
- 可选择固定或自动隐藏

### 2. 代码块增强

**功能描述**：增强代码块显示，支持语法高亮、行号、复制和折叠功能。

**功能特点**：

- 多语言语法高亮
- 显示行号
- 代码折叠
- 一键复制
- 代码搜索

**用户界面**：

- 语言标识
- 复制按钮
- 折叠/展开按钮
- 行号显示
- 代码主题选择

### 3. 图片查看器

**功能描述**：增强图片查看体验，支持缩放、全屏和幻灯片模式。

**功能特点**：

- 点击放大图片
- 支持缩放和平移
- 全屏查看
- 幻灯片模式浏览所有图片

**用户界面**：

- 图片悬停提示
- 放大/缩小控制
- 全屏按钮
- 幻灯片导航
- 图片说明显示

### 4. 离线阅读

**功能描述**：支持保存文章供离线阅读，包括文本、图片和样式。

**功能特点**：

- 保存完整文章
- 下载图片资源
- 保留样式和格式
- 阅读列表管理

**用户界面**：

- 保存按钮
- 阅读列表页面
- 文章管理界面
- 导出选项

## 用户场景

### 场景一：长文阅读

**用户**：小明，程序员，经常阅读技术博客和文档

**场景**：小明正在阅读一篇长篇技术文章，文章中包含大量代码示例和技术细节。

**使用流程**：

1. 小明点击扩展图标，启用阅读模式
2. 扩展自动提取文章内容，转换为 Markdown 并渲染
3. 小明调整字体大小和行高，使阅读更舒适
4. 使用自动生成的目录快速导航到感兴趣的章节
5. 对重要代码块使用高亮功能标记
6. 添加注释记录自己的想法
7. 保存文章供离线阅读

**价值**：小明可以在干净、无干扰的环境中高效阅读和学习，并保存重要内容供日后参考。

### 场景二：研究阅读

**用户**：小红，研究生，正在进行文献研究

**场景**：小红需要阅读和分析大量学术文章，并记录重要信息。

**使用流程**：

1. 小红打开一篇学术文章，启用阅读模式
2. 调整为护眼模式，减少眼睛疲劳
3. 使用文本选择工具高亮重要段落
4. 添加注释记录研究思路
5. 使用导出功能将文章及笔记导出为 Markdown
6. 将导出的内容整合到研究笔记中

**价值**：小红可以更高效地阅读和分析文献，减少眼睛疲劳，并轻松整合研究材料。

### 场景三：移动阅读

**用户**：小李，上班族，通勤时间喜欢阅读

**场景**：小李在地铁上使用手机浏览新闻和文章。

**使用流程**：

1. 小李在手机上打开一篇文章，启用阅读模式
2. 扩展自动调整为移动友好的布局
3. 增大字号，便于在移动设备上阅读
4. 使用夜间模式减少屏幕亮度
5. 保存文章，在没有网络的地铁区间继续阅读

**价值**：小李可以在移动设备上获得舒适的阅读体验，减少流量使用，并在离线环境中继续阅读。

## 设计原则

### 1. 简洁为先

- 界面设计简洁明了，避免视觉干扰
- 功能入口清晰，避免复杂的操作路径
- 默认设置适合大多数用户，减少配置负担

### 2. 用户控制

- 提供丰富的自定义选项，满足不同用户需求
- 所有功能可开启/关闭，尊重用户选择
- 提供撤销/恢复机制，允许用户试错

### 3. 一致性

- 在不同网站上提供一致的阅读体验
- 界面元素和交互模式保持一致
- 快捷键和操作逻辑符合用户习惯

### 4. 渐进增强

- 基本功能在所有环境下可用
- 高级功能在支持的环境中逐步启用
- 在性能受限设备上优雅降级

## 技术要求

### 依赖库

- **defuddle**: 用于内容提取
- **turndown**: 用于 HTML 到 Markdown 的转换
- **markdown-it**: 用于 Markdown 渲染
- **highlight.js**: 用于代码高亮
- **dayjs**: 用于日期处理
- **dompurify**: 用于 HTML 净化

### 浏览器兼容性

- Chrome 80+
- Firefox 78+
- Edge 80+
- Safari 14+

### 性能指标

- 首次渲染时间 < 1 秒
- 交互响应时间 < 100 毫秒
- 内存占用峰值 < 100MB
- 存储使用 < 10MB（不含缓存）

## 结论

阅读模式扩展 v1.8.0 版本通过采用 defuddle 提取内容、turndown 转换为 Markdown、markdown-it 渲染的新架构，将为用户提供更加一致、干净的阅读体验。新版本注重性能优化和用户体验改进，提供丰富的自定义选项和特色功能，满足不同用户的阅读需求。

通过智能内容提取、Markdown 转换和增强渲染三大核心功能，结合阅读设置面板、阅读工具栏、文本选择工具和阅读进度等用户体验改进，以及智能目录生成、代码块增强、图片查看器和离线阅读等特色功能，新版本将成为用户进行网页阅读的理想工具。
