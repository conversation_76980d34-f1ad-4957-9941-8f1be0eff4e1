# 阅读模式扩展 v1.8.0 开发路线图

## 概述

本路线图概述了阅读模式扩展 v1.8.0 版本的开发计划，包括各个阶段的任务、里程碑和时间线。v1.8.0 版本将对扩展进行重大重构，采用 defuddle 提取内容、turndown 转换为 Markdown、markdown-it 渲染的新架构。

## 开发阶段

### 第一阶段：准备与规划（1-2 周）

#### 任务

1. **技术调研**

   - 深入研究 defuddle 库的 API 和功能
   - 研究 turndown 的转换规则和自定义选项
   - 评估 markdown-it 及其插件生态系统
   - 分析 Obsidian Clipper 的实现方式和最佳实践

2. **架构设计**

   - 完成详细的架构设计文档
   - 定义模块接口和数据流
   - 设计错误处理和性能优化策略

3. **环境搭建**
   - 更新开发环境和依赖
   - 配置构建和测试工具
   - 设置性能监控工具

#### 里程碑

- 完成技术调研报告
- 完成详细架构设计文档
- 搭建好开发环境

### 第二阶段：核心组件开发（2-3 周）

#### 任务

1. **内容提取器开发**

   - 实现 DefuddleExtractor 类
   - 添加网站特定的提取规则
   - 开发元数据提取功能
   - 编写单元测试

2. **Markdown 转换器开发**

   - 实现 TurndownConverter 类
   - 自定义转换规则（代码块、表格、图片等）
   - 优化 Markdown 输出
   - 编写单元测试

3. **Markdown 渲染器开发**
   - 实现 MarkdownRenderer 类
   - 集成 markdown-it 插件
   - 开发主题支持
   - 编写单元测试

#### 里程碑

- 完成内容提取器并通过测试
- 完成 Markdown 转换器并通过测试
- 完成 Markdown 渲染器并通过测试

### 第三阶段：集成与用户界面（2-3 周）

#### 任务

1. **阅读模式管理器开发**

   - 实现 ReadingModeManager 类
   - 集成各个核心组件
   - 开发状态管理功能
   - 实现设置应用功能

2. **用户界面开发**

   - 开发阅读模式容器
   - 实现工具栏和控制面板
   - 开发主题切换功能
   - 实现响应式布局

3. **性能优化**
   - 实现懒加载策略
   - 开发 Web Worker 支持
   - 实现缓存机制
   - 优化 DOM 操作

#### 里程碑

- 完成阅读模式管理器
- 完成用户界面组件
- 性能达到目标指标

### 第四阶段：测试与优化（1-2 周）

#### 任务

1. **全面测试**

   - 进行单元测试和集成测试
   - 在各种网站上进行兼容性测试
   - 在不同浏览器上进行兼容性测试
   - 进行性能测试和基准测试

2. **Bug 修复与优化**

   - 修复测试中发现的 bug
   - 优化性能瓶颈
   - 改进错误处理
   - 优化用户体验

3. **文档完善**
   - 更新用户文档
   - 完善开发文档
   - 编写示例和教程

#### 里程碑

- 通过所有测试
- 修复所有关键 bug
- 完成文档

### 第五阶段：发布与推广（1 周）

#### 任务

1. **准备发布**

   - 准备发布说明
   - 更新版本号和清单文件
   - 准备宣传材料

2. **发布**

   - 发布到 Chrome 网上应用店
   - 发布到 Firefox 附加组件商店
   - 发布到其他浏览器扩展商店

3. **收集反馈**
   - 监控用户反馈
   - 收集使用数据
   - 规划后续更新

#### 里程碑

- 成功发布到各大浏览器扩展商店
- 收集初步用户反馈

## 时间线

| 阶段           | 时间范围   | 主要里程碑    |
| -------------- | ---------- | ------------- |
| 准备与规划     | 第 1-2 周  | 完成架构设计  |
| 核心组件开发   | 第 3-5 周  | 完成核心组件  |
| 集成与用户界面 | 第 6-8 周  | 完成集成和 UI |
| 测试与优化     | 第 9-10 周 | 通过所有测试  |
| 发布与推广     | 第 11 周   | 成功发布      |

## 风险与缓解策略

### 技术风险

1. **defuddle 提取准确性**

   - **风险**：defuddle 可能无法准确提取某些网站的内容
   - **缓解**：开发网站特定的提取规则，提供手动选择内容区域的功能

2. **Markdown 转换质量**

   - **风险**：复杂 HTML 结构可能无法完美转换为 Markdown
   - **缓解**：自定义转换规则，保留原始 HTML 作为备选

3. **性能问题**
   - **风险**：大型页面处理可能导致性能问题
   - **缓解**：使用 Web Workers、懒加载和增量处理

### 项目风险

1. **时间风险**

   - **风险**：开发时间可能超出预期
   - **缓解**：设置优先级，确保核心功能先完成

2. **兼容性风险**

   - **风险**：不同浏览器可能有兼容性问题
   - **缓解**：早期进行跨浏览器测试，使用 polyfill

3. **用户接受度风险**
   - **风险**：用户可能不适应新的实现方式
   - **缓解**：提供选项切换新旧模式，收集用户反馈

## 后续计划

### v1.8.1（短期）

- 修复 v1.8.0 发布后发现的 bug
- 优化性能和兼容性
- 根据用户反馈进行小改进

### v1.9.0（中期）

- 添加离线阅读功能
- 实现注释和高亮功能
- 增强自定义主题功能
- 改进移动设备支持

### v2.0.0（长期）

- 实现阅读列表和收藏功能
- 添加社交分享功能
- 开发云同步功能
- 支持更多内容格式（PDF、EPUB 等）

## 结论

阅读模式扩展 v1.8.0 版本的开发是一个重要的里程碑，将为用户提供更好的阅读体验。通过采用 defuddle、turndown 和 markdown-it 的新架构，我们可以简化代码结构，提高性能，并提供更一致、更干净的阅读体验。按照本路线图执行，我们预计可以在 11 周内完成开发和发布。
