# 阅读模式扩展 v1.8.0 愿景与设计

## 愿景

阅读模式扩展 v1.8.0 的核心愿景是**创造最佳网页阅读体验**，让用户能够专注于内容本身，而不受网页设计、广告和其他干扰元素的影响。通过采用 defuddle 提取内容、turndown 转换为 Markdown、markdown-it 渲染的新架构，我们致力于提供一个简洁、一致、高效的阅读环境。

## 设计理念

### 内容为王

在阅读模式扩展的设计中，我们始终坚持"内容为王"的理念。这意味着：

- **智能提取**：使用 defuddle 库精准识别和提取网页的主要内容，过滤掉广告、导航栏、侧边栏等干扰元素
- **保留价值**：保留对理解内容有价值的元素，如图片、表格、代码块等
- **结构优化**：优化内容结构，使标题层级、段落关系更加清晰

### 一致性体验

通过将提取的内容转换为 Markdown 并重新渲染，我们可以提供跨网站的一致阅读体验：

- **统一格式**：无论原始网页设计如何，都能提供统一的排版和样式
- **主题系统**：提供多种精心设计的主题，适应不同阅读场景和个人偏好
- **可预测性**：用户在不同网站上使用阅读模式时，可以获得可预测的体验

### 用户控制

我们相信用户应该拥有对阅读体验的完全控制权：

- **丰富设置**：提供全面的设置选项，允许用户自定义字体、颜色、间距等
- **预设系统**：支持保存和切换多个自定义预设，适应不同阅读场景
- **渐进式界面**：默认提供简洁界面，高级功能按需展开，不干扰基本阅读

### 性能优先

良好的阅读体验离不开出色的性能表现：

- **快速响应**：启用阅读模式的过程应该快速流畅，不影响用户体验
- **资源节约**：优化内存和 CPU 使用，确保在各种设备上都能流畅运行
- **离线支持**：支持离线阅读，减少网络依赖

## 设计方法论

### 以用户为中心

我们的设计过程始终以用户为中心，通过以下方式理解和满足用户需求：

- **用户场景**：识别典型用户场景，如长文阅读、研究阅读、移动阅读等
- **痛点分析**：分析用户在网页阅读中遇到的常见痛点，如干扰元素多、排版不一致、眼睛疲劳等
- **迭代改进**：基于用户反馈持续改进，确保功能真正解决用户问题

### 渐进式增强

我们采用渐进式增强的方法，确保扩展在各种环境下都能提供良好体验：

- **基础功能**：确保核心阅读功能在所有支持的浏览器和网站上可用
- **增强功能**：在支持的环境中提供高级功能，如代码高亮、数学公式渲染等
- **优雅降级**：在资源受限的环境中自动调整功能复杂度，保持流畅体验

### 模块化设计

通过模块化设计，我们可以提高代码质量和可维护性：

- **职责分离**：将内容提取、Markdown 转换、渲染等功能分离为独立模块
- **接口清晰**：定义清晰的模块接口，便于测试和替换
- **可扩展性**：设计可扩展的架构，便于添加新功能和支持新网站

## 设计决策

### 为什么选择 defuddle？

defuddle 是一个专为内容提取设计的库，具有以下优势：

- **智能分析**：能够智能分析网页结构，识别主要内容区域
- **准确性高**：相比传统的启发式算法，准确性更高，误判率更低
- **维护活跃**：由 Obsidian 团队维护，持续更新和改进
- **轻量级**：体积小，性能好，适合浏览器扩展使用

### 为什么选择 Markdown 作为中间格式？

选择 Markdown 作为 HTML 和最终渲染之间的中间格式有以下考虑：

- **简洁明了**：Markdown 语法简洁，容易理解和处理
- **结构化**：保留文档的结构信息，如标题层级、列表等
- **可移植**：便于导出和在其他工具中使用
- **可扩展**：可以通过扩展语法支持更多元素，如数学公式、图表等

### 为什么选择 markdown-it？

markdown-it 是一个功能强大的 Markdown 解析和渲染库：

- **性能优秀**：解析和渲染速度快，内存占用低
- **插件生态**：拥有丰富的插件生态，支持代码高亮、数学公式、表格等
- **可配置性**：高度可配置，可以根据需要启用或禁用功能
- **安全性**：内置 XSS 防护，确保渲染结果安全

## 设计挑战与解决方案

### 挑战一：内容提取准确性

**挑战**：不同网站的结构差异很大，单一算法难以适应所有情况。

**解决方案**：

- 结合 defuddle 的智能提取和网站特定规则
- 提供手动选择内容区域的备选方案
- 建立网站适配数据库，针对常见网站优化提取规则
- 收集用户反馈，持续改进提取算法

### 挑战二：复杂内容的 Markdown 转换

**挑战**：某些复杂的 HTML 结构（如嵌套表格、复杂布局）难以完美转换为 Markdown。

**解决方案**：

- 自定义 turndown 转换规则，处理特殊元素
- 对于无法转换的复杂结构，保留原始 HTML
- 提供转换质量反馈机制，让用户报告问题
- 针对常见问题模式开发专门的处理逻辑

### 挑战三：性能与资源占用

**挑战**：内容提取、转换和渲染过程可能占用大量资源，影响用户体验。

**解决方案**：

- 使用 Web Workers 将处理过程移至后台线程
- 实现增量处理和渲染，分批处理大型文档
- 缓存处理结果，避免重复处理
- 懒加载非关键资源，如语法高亮库

### 挑战四：跨浏览器兼容性

**挑战**：不同浏览器的 API 和行为存在差异，影响扩展的一致性。

**解决方案**：

- 使用 webextension-polyfill 抹平浏览器差异
- 建立全面的浏览器兼容性测试流程
- 针对特定浏览器的问题开发专门的解决方案
- 采用渐进式增强策略，确保核心功能在所有浏览器中可用

## 用户体验设计原则

### 简洁性

- **减少视觉噪音**：界面元素简洁，避免不必要的装饰
- **专注于内容**：UI 元素在不使用时自动隐藏，最大化内容区域
- **直观的交互**：操作方式符合用户直觉，减少学习成本

### 可访问性

- **键盘导航**：支持完全通过键盘操作
- **屏幕阅读器**：确保与屏幕阅读器兼容
- **颜色对比度**：符合 WCAG 2.1 AA 级标准
- **字体大小**：支持大范围的字体大小调整

### 响应式设计

- **适应不同屏幕**：从手机到桌面显示器，提供最佳布局
- **触摸友好**：在触摸设备上提供足够大的交互区域
- **方向适应**：适应设备旋转和窗口大小变化

### 反馈与透明度

- **操作反馈**：所有用户操作都有明确的视觉反馈
- **处理状态**：显示内容提取和处理的进度
- **错误处理**：友好地展示错误信息，并提供解决建议

## 视觉设计

### 排版系统

我们设计了一套完整的排版系统，确保阅读体验的一致性和舒适度：

- **字体选择**：提供精心挑选的字体组合，平衡可读性和美观性
- **比例关系**：标题、正文、引用等元素的大小比例关系符合经典排版原则
- **行高与间距**：根据字体大小自动调整最佳行高和段落间距
- **节奏感**：通过标题、段落、列表等元素的间距创造视觉节奏感

### 色彩系统

色彩系统基于以下原则设计：

- **主题变体**：提供浅色、深色、护眼等多种主题变体
- **对比度**：确保文本与背景的对比度符合可访问性标准
- **色彩和谐**：使用和谐的色彩组合，避免视觉疲劳
- **功能性色彩**：使用色彩传达功能和状态，如高亮、链接、错误等

### 图标与视觉元素

- **一致的图标风格**：所有图标遵循一致的设计语言
- **明确的语义**：图标设计明确传达其功能
- **最小装饰**：减少纯装饰性元素，专注于功能性设计
- **适应性**：图标和视觉元素能够适应不同的主题和尺寸

## 未来展望

阅读模式扩展 v1.8.0 是我们长期愿景的重要一步。展望未来，我们计划在以下方向继续发展：

### 智能化

- **个性化推荐**：基于阅读历史推荐排版设置
- **自动摘要**：生成文章摘要，帮助用户快速了解内容
- **关键词提取**：自动识别和高亮文章关键词

### 社交化

- **笔记分享**：分享阅读笔记和高亮
- **协作阅读**：支持多人同时阅读和评论
- **推荐系统**：基于相似用户的阅读偏好推荐内容

### 知识管理

- **知识图谱**：构建阅读内容之间的关联
- **标签系统**：对保存的文章进行分类和标记
- **全文搜索**：搜索保存文章的全文内容

## 结语

阅读模式扩展 v1.8.0 通过采用 defuddle 提取内容、turndown 转换为 Markdown、markdown-it 渲染的新架构，将为用户提供更加一致、干净的阅读体验。我们相信，这种方法不仅能够简化代码结构，提高性能，还能为用户创造真正专注于内容的阅读环境。

通过坚持"内容为王"、"一致性体验"、"用户控制"和"性能优先"的设计理念，结合以用户为中心、渐进式增强和模块化设计的方法论，我们致力于打造最佳的网页阅读工具，让用户能够在嘈杂的互联网环境中找到一片宁静的阅读空间。
