# Roadmap

## 近期目标（v1.7.x - 2023 年 Q4）

- **内容提取增强**
  - 完善极简阅读模式，提升内容提取准确率
  - 优化图片、表格、代码块等特殊内容的识别与展示
  - 支持更多主流网站，特别是复杂布局网站
- **用户体验优化**

  - 优化主题与排版自定义体验
  - 提升启动速度和性能表现
  - 增强快捷键支持和交互流程

- **本地优先基础**

  - 建立完善的 IndexedDB 本地存储架构
  - 增强本地存储与隐私保护机制
  - 确保所有功能在离线环境下正常工作

- **质量与测试**
  - 建立单元测试与端到端测试框架
  - 实现关键功能的性能测试
  - 建立常见网站兼容性测试用例库

## 中期目标（v1.8.x - 2024 年 Q1-Q2）

- **智能功能（本地实现）**

  - 支持本地 AI 摘要功能
  - 智能高亮与关键内容识别
  - 阅读难度评估与阅读时间估计

- **内容操作增强**
  - 增加 Markdown、PDF 导出能力
  - 支持内容注释与高亮功能
  - 实现内容片段收藏
- **国际化与无障碍**

  - 支持多语言界面与国际化
  - 增强无障碍特性
  - 支持屏幕阅读器

- **开发者生态**
  - 开放内容提取规则自定义接口
  - 建立主题系统扩展机制
  - 提供插件开发文档

## 转折点目标（v1.9.x - 2024 年 Q3）

- **深度定制**

  - 丰富主题市场，支持主题分享
  - 实现完整的用户自定义 CSS 支持
  - 允许用户定制 UI 组件位置和显示

- **高级阅读体验**

  - 实现沉浸式全屏阅读模式
  - 增强目录导航与内容大纲
  - 支持阅读进度同步与统计

- **性能优化**
  - 实现内容分段加载
  - 优化大型网页的处理性能
  - 减少内存占用

## 长期规划（v2.0 - 2024 年 Q4 及以后）

- **本地优先生态**

  - 与其他本地优先工具的集成（如 Obsidian、Logseq 等）
  - 本地端到端加密备份
  - 探索可选的点对点同步机制（不依赖中心化服务器）

- **技术更新**

  - 跟进 Chrome 扩展平台新特性
  - 探索使用 WebAssembly 优化性能
  - 采用最新前端技术栈，保持代码现代化

- **创新探索**

  - 探索浏览器原生支持的 AI 功能
  - 开发内容关联分析与知识图谱
  - 实现页面结构可视化

- **社区建设**
  - 建立用户社区，收集反馈
  - 开发贡献指南，鼓励社区参与
  - 建立插件生态系统

> 注意：本路线图专注于"本地优先"理念，所有功能均优先考虑本地实现，确保用户隐私和数据控制权。
