---
description: 
globs: 
alwaysApply: true
---
# 架构模式与设计模式

本项目采用了一系列架构和设计模式，以实现高内聚、低耦合、可扩展和高性能的代码结构。

## 核心架构模式

### 多上下文分离架构

Chrome扩展存在多个执行上下文，我们采用严格的关注点分离原则：

- **Content Script**：负责内容提取和阅读模式渲染
- **Background Script**：管理插件生命周期和全局状态
- **Popup**：轻量级控制面板，最小化状态
- **Options**：完整设置页面，独立应用

### 消息驱动架构

由于Chrome扩展各上下文之间的隔离，采用消息驱动的架构模式：

```typescript
// 消息类型定义
type MessageType = 
  | 'EXTRACT_CONTENT' 
  | 'APPLY_THEME'
  | 'SAVE_SETTINGS';

// 发送消息
function sendMessage(message: Message): Promise<any> {
  return chrome.runtime.sendMessage(message);
}

// 监听消息
function listenMessages(handler: (message: Message) => void) {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    // 处理消息
  });
}
```

### 响应式数据流

采用单向数据流模式，确保状态管理的可预测性：

- 使用Zustand实现轻量级状态管理
- 组件通过订阅机制响应状态变化
- 保持UI与状态的同步

### 分层设计

项目遵循清晰的分层原则，实现关注点分离：

1. **表现层**：React组件、UI渲染、用户交互
2. **业务逻辑层**：内容提取、设置管理、主题控制
3. **数据访问层**：IndexedDB访问、存储抽象
4. **外部服务层**：Chrome API、浏览器功能

## 关键设计模式

### 工厂模式

用于创建各种组件实例，尤其是在内容提取和渲染过程中：

```typescript
// 内容提取器工厂
class ExtractorFactory {
  static createExtractor(website: string): ContentExtractor {
    if (website.includes('medium.com')) {
      return new MediumExtractor();
    } else if (website.includes('github.com')) {
      return new GitHubExtractor();
    }
    // 默认使用通用提取器
    return new DefaultExtractor();
  }
}
```

### 策略模式

用于实现可替换的算法，特别是在内容提取和处理方面：

```typescript
// 内容提取策略接口
interface ExtractionStrategy {
  extract(document: Document): ExtractedContent;
}

// 实现不同的提取策略
class ReadabilityStrategy implements ExtractionStrategy {
  extract(document: Document): ExtractedContent {
    // 使用Readability算法提取
  }
}

class CustomRulesStrategy implements ExtractionStrategy {
  extract(document: Document): ExtractedContent {
    // 使用自定义规则提取
  }
}
```

### 观察者模式

实现组件间的松耦合通信和响应式更新：

- 设置变更通知
- UI状态同步
- 主题切换事件

### 适配器模式

使不兼容的接口能够协同工作：

```typescript
// 第三方库接口
interface ReadabilityParser {
  parse(): ReadabilityArticle;
}

// 我们的统一内容接口
interface ExtractedContent {
  title: string;
  content: string;
  author?: string;
  publishDate?: Date;
  images: ImageInfo[];
}

// 适配器
class ReadabilityAdapter implements ExtractedContent {
  private readability: ReadabilityParser;
  
  constructor(readability: ReadabilityParser) {
    this.readability = readability;
  }
  
  // 适配方法...
}
```

### 单例模式

确保全局服务和资源只有一个实例：

```typescript
// 存储管理器单例
class StorageManager {
  private static instance: StorageManager;
  
  static getInstance(): StorageManager {
    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager();
    }
    return StorageManager.instance;
  }
}
```

## 性能优化模式

### 懒加载模式

按需加载资源和功能：

```typescript
// 动态导入模块
async function loadProcessor() {
  const { ContentProcessor } = await import('./content-processor');
  return new ContentProcessor();
}
```

### Web Worker模式

将耗时计算放入Web Worker，避免阻塞主线程：

```typescript
// 在主线程中
function extractContentInWorker(html: string): Promise<ExtractedContent> {
  return new Promise((resolve, reject) => {
    const worker = new Worker('extraction-worker.js');
    worker.onmessage = (event) => {
      resolve(event.data);
      worker.terminate();
    };
    worker.postMessage({ html });
  });
}
```

### 虚拟化列表模式

处理长内容时使用虚拟滚动，减少DOM节点数量。

## 本地存储架构

使用IndexedDB进行本地存储，通过分层设计提供简洁的API：

```typescript
// 使用示例
await storage.set('settings', 'fontSize', 16);
const fontSize = await storage.get('settings', 'fontSize');
```

数据库模式设计：

- **settings**: 用户设置
- **themes**: 主题配置
- **history**: 阅读历史
- **annotations**: 高亮与笔记
- **extractorRules**: 内容提取规则

## 安全模式

### 内容安全模式

处理外部内容时防止XSS和其他注入攻击：

```typescript
import DOMPurify from 'dompurify';

function renderSafeHTML(content: string): string {
  return DOMPurify.sanitize(content, {
    ALLOWED_TAGS: ['p', 'a', 'ul', 'li', 'strong', 'em', 'h1', 'h2', 'h3', 'img'],
    ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'class']
  });
}
```

### 权限最小化模式

遵循最小权限原则，只请求必要的浏览器权限，不过度访问用户数据。
