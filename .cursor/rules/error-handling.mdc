---
description: 
globs: 
alwaysApply: true
---
# 错误处理策略

Chrome阅读插件处理不同上下文环境中的潜在错误，需要一套统一的错误处理策略，确保用户体验流畅，并快速恢复。

## 错误处理核心原则

1. **静默处理**：大多数错误不应中断用户体验
2. **优雅降级**：在功能不可用时提供备选方案
3. **透明记录**：记录错误，以便调试和改进
4. **结构化错误**：使用标准化错误类型和消息格式

## 错误类型与分类

定义标准化的错误类型层次结构：

```typescript
// 基础错误类型
export class ReaderError extends Error {
  constructor(
    message: string,
    public code: ErrorCode,
    public context?: unknown
  ) {
    super(message);
    this.name = 'ReaderError';
  }
}

// 具体错误类型
export class ContentExtractionError extends ReaderError {
  constructor(message: string, context?: unknown) {
    super(message, 'CONTENT_EXTRACTION_FAILED', context);
    this.name = 'ContentExtractionError';
  }
}

export class StorageError extends ReaderError {
  constructor(message: string, context?: unknown) {
    super(message, 'STORAGE_OPERATION_FAILED', context);
    this.name = 'StorageError';
  }
}

export class NetworkError extends ReaderError {
  constructor(message: string, context?: unknown) {
    super(message, 'NETWORK_REQUEST_FAILED', context);
    this.name = 'NetworkError';
  }
}

export class RenderError extends ReaderError {
  constructor(message: string, context?: unknown) {
    super(message, 'RENDER_FAILED', context);
    this.name = 'RenderError';
  }
}

// 错误代码枚举
export type ErrorCode = 
  | 'CONTENT_EXTRACTION_FAILED'
  | 'STORAGE_OPERATION_FAILED'
  | 'NETWORK_REQUEST_FAILED'
  | 'RENDER_FAILED'
  | 'PERMISSION_DENIED'
  | 'TIMEOUT_EXCEEDED'
  | 'VALIDATION_FAILED'
  | 'UNEXPECTED_STATE';
```

## 错误处理模式

### 使用Try-Catch与Async/Await

在异步函数中优雅处理错误：

```typescript
async function extractAndRender() {
  try {
    const html = document.documentElement.outerHTML;
    const content = await extractContent(html);
    renderContent(content);
  } catch (error) {
    if (error instanceof ContentExtractionError) {
      // 降级：显示简单的阅读视图
      renderSimpleView();
      logError(error);
    } else {
      // 重新抛出未知错误
      throw error;
    }
  }
}
```

### 使用ErrorBoundary处理React组件错误

在React组件中使用ErrorBoundary防止整个应用崩溃：

```typescript
class ErrorBoundary extends React.Component<{ 
  fallback?: React.ReactNode,
  children: React.ReactNode 
}> {
  state = { hasError: false, error: null };
  
  static getDerivedStateFromError(error: any) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: any, errorInfo: any) {
    logComponentError(error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return this.props.fallback || <p>内容加载失败</p>;
    }
    
    return this.props.children;
  }
}

// 使用错误边界
function ReaderView() {
  return (
    <ErrorBoundary fallback={<SimpleFallbackView />}>
      <ContentRenderer />
    </ErrorBoundary>
  );
}
```

## 错误日志与监控

### 本地错误日志

实现本地错误日志以便调试：

```typescript
// 日志级别定义
type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// 错误日志接口
interface ErrorLog {
  id: string;
  timestamp: number;
  code: ErrorCode;
  message: string;
  context?: unknown;
  stack?: string;
}

// 日志管理器
class LogManager {
  private readonly MAX_LOGS = 100;
  private db: IDBDatabase | null = null;
  
  constructor() {
    this.initDatabase();
  }
  
  // 初始化日志数据库
  private async initDatabase() {
    try {
      const request = indexedDB.open('reader_logs', 1);
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBRequest).result;
        if (!db.objectStoreNames.contains('error_logs')) {
          const store = db.createObjectStore('error_logs', { keyPath: 'id' });
          store.createIndex('timestamp', 'timestamp', { unique: false });
        }
      };
      
      request.onsuccess = (event) => {
        this.db = (event.target as IDBRequest).result;
        this.pruneOldLogs();
      };
      
      request.onerror = (event) => {
        console.error('无法初始化日志数据库', event);
      };
    } catch (error) {
      console.error('日志数据库初始化失败', error);
    }
  }
  
  // 记录错误
  async logError(error: ReaderError): Promise<void> {
    if (!this.db) return;
    
    const log: ErrorLog = {
      id: crypto.randomUUID(),
      timestamp: Date.now(),
      code: error.code,
      message: error.message,
      context: error.context,
      stack: error.stack
    };
    
    try {
      const transaction = this.db.transaction(['error_logs'], 'readwrite');
      const store = transaction.objectStore('error_logs');
      await store.add(log);
    } catch (error) {
      console.error('无法记录错误', error);
    }
  }
  
  // 清理旧日志，确保不超过最大日志数
  private async pruneOldLogs(): Promise<void> {
    if (!this.db) return;
    
    try {
      const transaction = this.db.transaction(['error_logs'], 'readwrite');
      const store = transaction.objectStore('error_logs');
      const index = store.index('timestamp');
      
      const countRequest = store.count();
      countRequest.onsuccess = () => {
        const count = countRequest.result;
        if (count <= this.MAX_LOGS) return;
        
        const deleteCount = count - this.MAX_LOGS;
        const cursorRequest = index.openCursor();
        let processed = 0;
        
        cursorRequest.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;
          if (cursor && processed < deleteCount) {
            store.delete(cursor.primaryKey);
            processed++;
            cursor.continue();
          }
        };
      };
    } catch (error) {
      console.error('无法清理旧日志', error);
    }
  }
  
  // 获取所有日志
  async getLogs(): Promise<ErrorLog[]> {
    if (!this.db) return [];
    
    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(['error_logs'], 'readonly');
        const store = transaction.objectStore('error_logs');
        const request = store.getAll();
        
        request.onsuccess = () => {
          resolve(request.result);
        };
        
        request.onerror = () => {
          reject(new Error('获取日志失败'));
        };
      } catch (error) {
        reject(error);
      }
    });
  }
}

// 创建单例日志管理器
export const logger = new LogManager();
```

## 优雅降级策略

### 内容提取失败时的降级

当智能内容提取失败时的降级策略：

```typescript
async function getArticleContent(doc: Document): Promise<ArticleContent> {
  try {
    // 尝试使用高级算法提取
    return await extractWithAdvancedAlgorithm(doc);
  } catch (error) {
    logger.logError(new ContentExtractionError('高级提取失败', { url: location.href }));
    
    try {
      // 降级到基本算法
      return extractWithBasicAlgorithm(doc);
    } catch (secondError) {
      logger.logError(new ContentExtractionError('基本提取失败', { url: location.href }));
      
      // 最终降级：使用通用选择器
      return {
        title: doc.title || '未知标题',
        content: Array.from(doc.querySelectorAll('p, h1, h2, h3, h4, h5, h6')),
        readingTime: 0,
        rawHtml: doc.body.innerHTML
      };
    }
  }
}
```

### 存储访问失败处理

当存储操作失败时的处理策略：

```typescript
class StorageService {
  async saveArticle(article: Article): Promise<boolean> {
    try {
      // 首选IndexedDB存储
      return await this.saveToIndexedDB(article);
    } catch (error) {
      logger.logError(new StorageError('IndexedDB存储失败', { article }));
      
      try {
        // 降级到localStorage
        return this.saveToLocalStorage(article);
      } catch (fallbackError) {
        logger.logError(new StorageError('本地存储失败', { article }));
        // 最终放弃存储，但不影响核心功能
        return false;
      }
    }
  }
  
  private async saveToIndexedDB(article: Article): Promise<boolean> {
    // IndexedDB实现
    return true;
  }
  
  private saveToLocalStorage(article: Article): boolean {
    try {
      // 简化对象以适应localStorage限制
      const simplifiedArticle = this.simplifyForLocalStorage(article);
      const key = `article_${article.url.replace(/[^\w]/g, '_')}`;
      localStorage.setItem(key, JSON.stringify(simplifiedArticle));
      return true;
    } catch (error) {
      throw new StorageError('localStorage存储失败', error);
    }
  }
  
  private simplifyForLocalStorage(article: Article): SimpleArticle {
    // 移除大型内容以适应localStorage限制
    return {
      url: article.url,
      title: article.title,
      excerpt: article.excerpt,
      savedAt: article.savedAt
    };
  }
}
```

## 自动恢复机制

### 异步操作重试

对关键异步操作实现重试机制：

```typescript
async function withRetry<T>(
  operation: () => Promise<T>,
  options: {
    retries?: number;
    delay?: number;
    backoff?: number;
    shouldRetry?: (error: any) => boolean;
  } = {}
): Promise<T> {
  const {
    retries = 3,
    delay = 300,
    backoff = 2,
    shouldRetry = () => true
  } = options;
  
  let lastError: any;
  let currentDelay = delay;
  
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (attempt >= retries || !shouldRetry(error)) {
        throw error;
      }
      
      await new Promise(resolve => setTimeout(resolve, currentDelay));
      currentDelay *= backoff;
    }
  }
  
  throw lastError;
}

// 使用示例
async function fetchArticleMetadata(url: string): Promise<ArticleMetadata> {
  return withRetry(
    () => fetch(url).then(r => r.json()),
    {
      retries: 3,
      shouldRetry: (error) => {
        // 只有在网络错误或5xx错误时重试
        return error instanceof NetworkError || 
               (error.status >= 500 && error.status < 600);
      }
    }
  );
}
```

### 状态恢复

在扩展崩溃或页面刷新后恢复状态：

```typescript
class StateRecoveryManager {
  // 保存当前状态
  saveState(key: string, state: unknown): void {
    try {
      const serializedState = JSON.stringify(state);
      localStorage.setItem(`state_${key}`, serializedState);
      localStorage.setItem(`state_${key}_timestamp`, String(Date.now()));
    } catch (error) {
      logger.logError(new StorageError('无法保存状态', { key, error }));
    }
  }
  
  // 恢复状态
  loadState<T>(key: string, maxAge: number = 3600000): T | null {
    try {
      const timestamp = localStorage.getItem(`state_${key}_timestamp`);
      if (!timestamp) return null;
      
      const age = Date.now() - Number(timestamp);
      if (age > maxAge) {
        this.clearState(key);
        return null;
      }
      
      const serializedState = localStorage.getItem(`state_${key}`);
      if (!serializedState) return null;
      
      return JSON.parse(serializedState) as T;
    } catch (error) {
      logger.logError(new StorageError('无法恢复状态', { key, error }));
      this.clearState(key);
      return null;
    }
  }
  
  // 清除旧状态
  clearState(key: string): void {
    try {
      localStorage.removeItem(`state_${key}`);
      localStorage.removeItem(`state_${key}_timestamp`);
    } catch (error) {
      // 忽略清除错误
    }
  }
}

// 使用状态恢复管理器
const stateRecovery = new StateRecoveryManager();

// React组件中使用
function ReaderApp() {
  const [readingState, setReadingState] = useState(() => {
    // 尝试恢复状态
    return stateRecovery.loadState<ReadingState>('reader') || {
      fontSize: 16,
      theme: 'light',
      readingPosition: 0
    };
  });
  
  // 保存状态
  useEffect(() => {
    stateRecovery.saveState('reader', readingState);
  }, [readingState]);
  
  // ...组件实现
}
```

## 错误通知策略

### 用户友好错误提示

根据错误类型提供用户友好的错误消息：

```typescript
// 错误消息映射
const userFriendlyMessages: Record<ErrorCode, string> = {
  CONTENT_EXTRACTION_FAILED: '无法提取页面内容，正在使用基本阅读视图',
  STORAGE_OPERATION_FAILED: '无法保存您的偏好设置',
  NETWORK_REQUEST_FAILED: '网络连接出现问题',
  RENDER_FAILED: '显示内容时出现问题',
  PERMISSION_DENIED: '缺少所需权限',
  TIMEOUT_EXCEEDED: '操作超时，请重试',
  VALIDATION_FAILED: '输入验证失败',
  UNEXPECTED_STATE: '发生意外错误'
};

// 错误通知组件
function ErrorNotification({ error }: { error: ReaderError | null }) {
  if (!error) return null;
  
  const message = userFriendlyMessages[error.code] || '发生错误';
  
  return (
    <div className="error-notification">
      <p>{message}</p>
      {error.code === 'PERMISSION_DENIED' && (
        <button onClick={requestPermissions}>授予权限</button>
      )}
    </div>
  );
}

// 错误状态管理
function useErrorState() {
  const [error, setError] = useState<ReaderError | null>(null);
  
  // 自动清除错误
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);
  
  return {
    error,
    setError,
    clearError: () => setError(null)
  };
}
```

## 防错设计

### 输入验证

验证用户输入以防止错误：

```typescript
// 验证函数
function validateSettings(settings: UserSettings): ValidationResult {
  const errors: Record<string, string> = {};
  
  // 验证字体大小
  if (settings.fontSize < 10 || settings.fontSize > 32) {
    errors.fontSize = '字体大小必须在10-32之间';
  }
  
  // 验证行高
  if (settings.lineHeight < 1 || settings.lineHeight > 3) {
    errors.lineHeight = '行高必须在1-3之间';
  }
  
  // 验证页边距
  if (settings.margin < 0 || settings.margin > 100) {
    errors.margin = '页边距必须在0-100之间';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

// 使用验证
function saveSettings(settings: UserSettings) {
  const validation = validateSettings(settings);
  
  if (!validation.isValid) {
    throw new ReaderError(
      '设置验证失败',
      'VALIDATION_FAILED',
      validation.errors
    );
  }
  
  // 保存已验证的设置
  return storage.saveSettings(settings);
}
```

### 防止并发错误

使用节流和锁机制防止并发错误：

```typescript
// 创建互斥锁
class Mutex {
  private locked = false;
  private waitQueue: Array<() => void> = [];
  
  async acquire(): Promise<() => void> {
    if (!this.locked) {
      this.locked = true;
      return () => this.release();
    }
    
    return new Promise<() => void>(resolve => {
      this.waitQueue.push(() => {
        this.locked = true;
        resolve(() => this.release());
      });
    });
  }
  
  private release(): void {
    if (this.waitQueue.length > 0) {
      const next = this.waitQueue.shift();
      if (next) next();
    } else {
      this.locked = false;
    }
  }
}

// 使用互斥锁同步访问
class SafeStorage {
  private mutex = new Mutex();
  
  async updateSettings(update: Partial<UserSettings>): Promise<UserSettings> {
    const release = await this.mutex.acquire();
    
    try {
      const currentSettings = await this.getSettings();
      const newSettings = { ...currentSettings, ...update };
      const validated = validateSettings(newSettings);
      
      if (!validated.isValid) {
        throw new ReaderError('设置验证失败', 'VALIDATION_FAILED', validated.errors);
      }
      
      await this.saveSettings(newSettings);
      return newSettings;
    } finally {
      release();
    }
  }
  
  private async getSettings(): Promise<UserSettings> {
    // 获取设置实现
    return { fontSize: 16, lineHeight: 1.5, margin: 20 };
  }
  
  private async saveSettings(settings: UserSettings): Promise<void> {
    // 保存设置实现
  }
}
```

## 错误预防检查

### 前置条件检查

在执行操作前验证条件：

```typescript
function precondition<T>(
  condition: boolean, 
  errorMessage: string,
  errorCode: ErrorCode = 'UNEXPECTED_STATE'
): asserts condition {
  if (!condition) {
    throw new ReaderError(errorMessage, errorCode);
  }
}

// 使用前置条件检查
function extractContent(html: string, options: ExtractionOptions) {
  precondition(!!html, '没有提供HTML内容', 'CONTENT_EXTRACTION_FAILED');
  precondition(options.minContentLength >= 0, '最小内容长度不能为负数', 'VALIDATION_FAILED');
  
  // 继续处理...
}
```

## 错误收集与分析

### 错误模式分析

定期分析收集的错误：

```typescript
class ErrorAnalyzer {
  async analyzeErrors(): Promise<ErrorAnalysis> {
    const logs = await logger.getLogs();
    
    // 按错误代码分组
    const byCode = logs.reduce((acc, log) => {
      const code = log.code;
      if (!acc[code]) acc[code] = [];
      acc[code].push(log);
      return acc;
    }, {} as Record<ErrorCode, ErrorLog[]>);
    
    // 提取最常见错误
    const mostCommon = Object.entries(byCode)
      .sort((a, b) => b[1].length - a[1].length)
      .slice(0, 3)
      .map(([code, logs]) => ({
        code: code as ErrorCode,
        count: logs.length,
        examples: logs.slice(0, 3)
      }));
    
    // 计算错误趋势
    const last24Hours = logs.filter(
      log => log.timestamp > Date.now() - 24 * 60 * 60 * 1000
    ).length;
    
    const previous24Hours = logs.filter(
      log => log.timestamp > Date.now() - 48 * 60 * 60 * 1000 &&
             log.timestamp <= Date.now() - 24 * 60 * 60 * 1000
    ).length;
    
    return {
      totalErrors: logs.length,
      byCode,
      mostCommon,
      trend: {
        last24Hours,
        previous24Hours,
        change: last24Hours - previous24Hours
      }
    };
  }
}
```

## 安全错误处理

### 安全敏感信息处理

确保错误记录不包含敏感信息：

```typescript
// 清理敏感数据
function sanitizeErrorContext(context: unknown): unknown {
  if (!context) return context;
  
  if (typeof context === 'object') {
    const sanitized = { ...context };
    
    // 移除敏感字段
    const sensitiveFields = ['password', 'token', 'auth', 'key', 'secret'];
    
    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '[REDACTED]';
      }
    }
    
    // 递归处理嵌套对象
    for (const [key, value] of Object.entries(sanitized)) {
      if (typeof value === 'object' && value !== null) {
        sanitized[key] = sanitizeErrorContext(value);
      }
    }
    
    return sanitized;
  }
  
  return context;
}

// 安全记录错误
function logErrorSafely(error: ReaderError): void {
  // 清理敏感数据
  if (error.context) {
    error.context = sanitizeErrorContext(error.context);
  }
  
  // 记录清理后的错误
  logger.logError(error);
}
```
