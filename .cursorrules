# Chrome阅读插件项目规则

## 项目概述
这是一款专注于"简洁易用、本地优先"的Chrome阅读插件，旨在提供极简、优雅的网页阅读体验，同时保护用户隐私。

## 命名约定

### 文件命名
- React组件文件使用PascalCase: `ReaderView.tsx`, `ThemeSelector.tsx`
- 工具函数、钩子使用camelCase: `useStorage.ts`, `extractContent.ts`
- 样式文件与组件同名，使用.module.css后缀: `ReaderView.module.css`
- 测试文件与被测文件同名，添加.test或.spec: `extractContent.test.ts`
- 常量文件使用全大写下划线: `THEME_CONSTANTS.ts`

### 变量命名
- 组件使用PascalCase: `<ReaderView />`, `<ThemeSelector />`
- 变量、函数使用camelCase: `extractContent`, `themeColor`
- 布尔类型变量使用is/has/should前缀: `isReaderMode`, `hasHistory`
- 常量使用全大写下划线: `DEFAULT_FONT_SIZE`, `STORAGE_KEYS`
- 类型和接口使用PascalCase，接口不使用I前缀: `ThemeConfig`, `ExtractedContent`

## 代码组织

### 目录结构
- `/src/background`: 后台脚本相关代码
- `/src/content`: 内容脚本相关代码
- `/src/popup`: 弹窗UI相关代码
- `/src/storage`: 存储相关代码
- `/src/utils`: 通用工具函数
- `/src/ui`: 通用UI组件
- `/src/types`: 类型定义
- `/src/constants`: 常量定义
- `/src/hooks`: 自定义钩子
- `/src/presets`: 主题和排版预设

### 组件结构
- 每个组件使用自己的目录
- 包含组件文件、样式文件、测试文件、类型定义
- 使用index.ts导出组件

```
/src/content/components/ReaderView/
  ReaderView.tsx
  ReaderView.module.css
  ReaderView.test.tsx
  types.ts
  index.ts
```

## 编码规范

### TypeScript
- 使用严格模式: `"strict": true`
- 明确声明所有类型，减少any使用
- 使用类型而非接口定义普通对象
- 使用接口定义组件props: `interface ReaderViewProps {}`
- 使用函数组件和hooks，不使用类组件

### React
- 使用函数组件和Hooks
- 使用memo优化渲染性能
- 组件props使用解构: `function Component({ prop1, prop2 }: Props)`
- 状态管理使用Zustand，避免复杂的Redux配置

### CSS
- 使用Tailwind CSS原子类
- 复杂样式使用CSS Modules避免冲突
- 使用CSS变量定义主题和排版变量
- 遵循移动优先的响应式设计原则

## 性能优化

### 懒加载策略
- 使用React.lazy和Suspense懒加载组件
- 内容提取和处理使用Web Workers
- 非核心功能延迟加载

### 渲染优化
- 大型内容使用虚拟列表
- 使用memo和useMemo避免不必要渲染
- 使用useCallback稳定回调引用

### 存储优化
- 大型数据使用压缩存储
- 实现LRU缓存机制清理过期数据
- 批量操作IndexedDB减少事务开销

## 安全原则

### 内容安全
- 使用DOMPurify净化HTML内容
- 所有用户输入进行验证和转义
- 使用CSP限制内容执行权限

### 隐私保护
- 所有数据仅存储在本地
- 不收集任何用户信息或使用统计
- 遵循最小权限原则

## 开发流程

### 提交规范
- 使用语义化提交消息: `feat:`, `fix:`, `docs:`, `style:`, `refactor:`, `perf:`, `test:`, `chore:`
- 每个提交专注于单一功能或修复
- 保持提交粒度适中，便于回滚和审查

### 测试策略
- 每个核心功能编写单元测试
- UI组件使用快照测试
- 实现关键流程的端到端测试
- 定期进行性能测试

### 文档要求
- 所有公共API添加JSDoc注释
- 复杂逻辑添加内联注释
- 更新README保持与代码同步
- 重要决策记录在文档中

## 本地优先原则

1. 所有用户数据必须存储在本地，不得上传到服务器
2. 功能实现优先考虑不依赖网络的方案
3. 任何潜在的网络请求必须是可选的，且默认关闭
4. 用户隐私保护高于一切其他考虑

## 界面设计原则

1. 保持界面简洁，专注于内容
2. 交互设计直观，减少学习成本
3. 视觉元素使用克制，避免过度装饰
4. 深色/浅色主题提供同等优质体验
5. 响应式设计，适应不同屏幕尺寸

## 发布检查清单

1. 所有测试通过
2. 性能指标达标
3. 代码无lint警告
4. 类型检查无错误
5. 文档更新完成
6. 版本号符合语义化版本规范
7. 更新日志完善
8. 构建产物经过验证 