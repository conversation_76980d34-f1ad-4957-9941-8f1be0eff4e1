/* 新的 Popup 样式 */
.preset-card {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.preset-card:hover {
  transform: translateY(-2px);
}

.preset-card.active {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.preset-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, #3b82f6, #60a5fa);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.preset-card.active::before {
  opacity: 1;
}

/* 自定义滑块样式 */
input[type=range] {
  height: 8px;
  -webkit-appearance: none;
  margin: 10px 0;
  border-radius: 8px;
}

input[type=range]:focus {
  outline: none;
}

input[type=range]::-webkit-slider-runnable-track {
  width: 100%;
  height: 8px;
  cursor: pointer;
  animate: 0.2s;
  box-shadow: 0px 0px 0px #000000;
  background: #E5E7EB;
  border-radius: 8px;
  border: 0px solid #000000;
}

.dark input[type=range]::-webkit-slider-runnable-track {
  background: #374151;
}

input[type=range]::-webkit-slider-thumb {
  box-shadow: 0px 0px 1px #828282;
  border: 0px solid #FFFFFF;
  height: 18px;
  width: 18px;
  border-radius: 25px;
  background: #3B82F6;
  cursor: pointer;
  -webkit-appearance: none;
  margin-top: -5px;
  transition: all 0.2s ease;
}

input[type=range]::-webkit-slider-thumb:hover {
  background: #2563EB;
  transform: scale(1.1);
}

.dark input[type=range]::-webkit-slider-thumb {
  background: #60A5FA;
}

.dark input[type=range]::-webkit-slider-thumb:hover {
  background: #93C5FD;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slideIn {
  animation: slideIn 0.3s ease-in-out;
}

/* 自定义滚动条 */
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

/* 平滑过渡效果 */
* {
  transition: background-color 0.3s, border-color 0.3s, color 0.3s;
}

/* 主题切换按钮 */
.theme-toggle {
  position: relative;
  width: 40px;
  height: 20px;
  border-radius: 10px;
  background-color: #e5e7eb;
  cursor: pointer;
  transition: background-color 0.3s;
}

.dark .theme-toggle {
  background-color: #4b5563;
}

.theme-toggle::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: white;
  transition: transform 0.3s, background-color 0.3s;
}

.dark .theme-toggle::after {
  transform: translateX(20px);
  background-color: #60a5fa;
}

/* 按钮悬停效果 */
button:not(:disabled):hover {
  transform: translateY(-1px);
}

button:not(:disabled):active {
  transform: translateY(0);
}

/* 卡片悬停效果 */
.hover-card {
  transition: all 0.2s ease;
}

.hover-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 缩放效果 */
.scale-102 {
  transform: scale(1.02);
}

.scale-105 {
  transform: scale(1.05);
}

/* 渐变背景 */
.bg-gradient-brand {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
}

.dark .bg-gradient-brand {
  background: linear-gradient(135deg, #2563eb, #3b82f6);
}